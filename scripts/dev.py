#!/usr/bin/env python3
"""
开发工具脚本

提供代码格式化、类型检查、测试等开发任务
"""

import subprocess
import sys
from pathlib import Path
from typing import List, Optional


def run_command(cmd: List[str], cwd: Optional[Path] = None) -> int:
    """运行命令并返回退出码"""
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, cwd=cwd)
    return result.returncode


def format_code() -> int:
    """格式化代码"""
    print("🎨 Formatting code with black and isort...")
    
    # 运行 black
    black_result = run_command(["uv", "run", "black", "main.py", "src/"])
    
    # 运行 isort
    isort_result = run_command(["uv", "run", "isort", "main.py", "src/"])
    
    return max(black_result, isort_result)


def lint_code() -> int:
    """代码检查"""
    print("🔍 Linting code with flake8...")
    return run_command(["uv", "run", "flake8", "main.py", "src/"])


def type_check() -> int:
    """类型检查"""
    print("🔬 Type checking with mypy...")
    return run_command(["uv", "run", "mypy", "main.py", "src/"])


def run_tests() -> int:
    """运行测试"""
    print("🧪 Running tests with pytest...")
    return run_command(["uv", "run", "pytest"])


def check_all() -> int:
    """运行所有检查"""
    print("🚀 Running all checks...")
    
    results = []
    
    # 格式化代码
    results.append(format_code())
    
    # 代码检查
    results.append(lint_code())
    
    # 类型检查
    results.append(type_check())
    
    # 运行测试
    results.append(run_tests())
    
    # 返回最大退出码
    max_result = max(results)
    
    if max_result == 0:
        print("✅ All checks passed!")
    else:
        print("❌ Some checks failed!")
    
    return max_result


def start_server() -> int:
    """启动开发服务器"""
    print("🚀 Starting CodeStandardMCP server...")
    return run_command(["uv", "run", "python", "main.py"])


def install_dev_deps() -> int:
    """安装开发依赖"""
    print("📦 Installing development dependencies...")
    return run_command(["uv", "sync", "--extra", "dev"])


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("Usage: python scripts/dev.py <command>")
        print("Commands:")
        print("  format     - Format code with black and isort")
        print("  lint       - Lint code with flake8")
        print("  typecheck  - Type check with mypy")
        print("  test       - Run tests with pytest")
        print("  check      - Run all checks")
        print("  server     - Start development server")
        print("  install    - Install development dependencies")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "format":
        sys.exit(format_code())
    elif command == "lint":
        sys.exit(lint_code())
    elif command == "typecheck":
        sys.exit(type_check())
    elif command == "test":
        sys.exit(run_tests())
    elif command == "check":
        sys.exit(check_all())
    elif command == "server":
        sys.exit(start_server())
    elif command == "install":
        sys.exit(install_dev_deps())
    else:
        print(f"Unknown command: {command}")
        sys.exit(1)


if __name__ == "__main__":
    main()
