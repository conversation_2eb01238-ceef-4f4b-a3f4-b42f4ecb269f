# CodeStandardMCP - 编码规范即服务

CodeStandardMCP 是一个基于 FastMCP 2.0 框架的专业编码规范管理服务器，提供规范获取、搜索、合规性检查和管理功能。

## 🚀 功能特性

### 核心功能

- **📥 规范获取 (StandardFetcher)**: 从外部API获取编码规范并缓存到本地
- **🔍 规范浏览 (StandardExplorer)**: 列出、搜索和浏览编码规范
- **✅ 合规性检查 (ComplianceChecker)**: 检查代码是否符合编码规范
- **🛠️ 规范管理 (StandardsManager)**: 更新、验证和管理编码规范

### 支持的语言和框架

- **Python**: Django, Flask, FastAPI
- **JavaScript/TypeScript**: React, Vue, Angular, Express
- **Java**: Spring, Hibernate, Maven
- **C#**: .NET Core, ASP.NET, Entity Framework
- **Go**: Gin, Echo, Fiber
- **Rust**: Actix, Rocket, Warp
- **PHP**: Laravel, Symfony, CodeIgniter
- **Ruby**: Rails, Sinatra, Grape

## 📦 安装和配置

### 环境要求

- Python 3.8+
- FastMCP 2.0+
- 依赖包：pydantic, aiofiles, httpx

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd CET-mcp
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 配置环境变量
```bash
# 可选配置
export CSMCP_DATA_DIR="./data"
export CSMCP_LOG_LEVEL="INFO"
export CSMCP_CACHE_TTL="24"
export CSMCP_HTTP_TIMEOUT="30"
```

4. 启动服务器
```bash
python main.py
```

服务器将在 `http://0.0.0.0:18023` 启动。

## 🔧 MCP 工具接口

### 1. fetch_standard
从外部API获取编码规范并缓存到本地

```json
{
  "name": "fetch_standard",
  "arguments": {
    "domain": "coding_standards",
    "language": "python",
    "framework": "django",
    "force_refresh": false
  }
}
```

### 2. list_standards
列出所有可用的编码规范

```json
{
  "name": "list_standards",
  "arguments": {
    "language": "python",
    "framework": "django"
  }
}
```

### 3. search_standard
在编码规范中搜索特定内容

```json
{
  "name": "search_standard",
  "arguments": {
    "query": "variable naming",
    "language": "python",
    "max_results": 10
  }
}
```

### 4. get_rule_details
获取特定规则的详细信息

```json
{
  "name": "get_rule_details",
  "arguments": {
    "rule_id": "PEP8-N001",
    "language": "python"
  }
}
```

### 5. check_compliance
检查代码是否符合编码规范

```json
{
  "name": "check_compliance",
  "arguments": {
    "code": "def myFunction():\n    pass",
    "language": "python",
    "framework": "django"
  }
}
```

### 6. explain_violation
解释违规规则和提供修复建议

```json
{
  "name": "explain_violation",
  "arguments": {
    "rule_id": "PEP8-N001",
    "language": "python"
  }
}
```

### 7. update_standards
更新本地编码规范数据

```json
{
  "name": "update_standards",
  "arguments": {
    "language": "python",
    "force_update": false
  }
}
```

### 8. validate_standard
验证编码规范文档的完整性

```json
{
  "name": "validate_standard",
  "arguments": {
    "language": "python",
    "framework": "django"
  }
}
```

### 9. clean_cache
清理缓存和临时文件

```json
{
  "name": "clean_cache",
  "arguments": {
    "max_age_days": 7,
    "max_size_mb": 100
  }
}
```

## 📁 项目结构

```
CET-mcp/
├── main.py                     # MCP服务器主文件
├── src/                        # 源代码目录
│   ├── config/                 # 配置管理
│   │   └── settings.py
│   ├── models/                 # 数据模型
│   │   └── standard.py
│   ├── services/               # 核心服务
│   │   ├── standard_fetcher.py
│   │   ├── standard_explorer.py
│   │   ├── compliance_checker.py
│   │   └── standards_manager.py
│   ├── storage/                # 存储管理
│   │   ├── file_manager.py
│   │   ├── cache_manager.py
│   │   └── index_manager.py
│   └── utils/                  # 工具函数
│       ├── validators.py
│       └── parsers.py
├── data/                       # 数据目录
│   ├── standards/              # 规范文档存储
│   ├── cache/                  # 缓存文件
│   ├── index/                  # 索引文件
│   └── config.example.json     # 配置示例
├── requirements.txt            # Python依赖
└── README.md                   # 项目文档
```

## 🔧 配置说明

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `CSMCP_DATA_DIR` | `./data` | 数据存储目录 |
| `CSMCP_LOG_LEVEL` | `INFO` | 日志级别 |
| `CSMCP_CACHE_TTL` | `24` | 缓存生存时间(小时) |
| `CSMCP_HTTP_TIMEOUT` | `30` | HTTP请求超时时间(秒) |
| `CSMCP_MAX_CODE_LENGTH` | `10000` | 最大代码长度 |
| `CSMCP_AUTO_UPDATE_DAYS` | `7` | 自动更新间隔(天) |
| `CSMCP_AUTO_CLEANUP_ENABLED` | `true` | 是否启用自动清理 |

### 配置文件

参考 `data/config.example.json` 进行详细配置。

## 🛠️ 开发指南

### 开发环境设置

```bash
# 克隆项目
git clone <repository-url>
cd CET-mcp

# 安装开发依赖
make dev
# 或者
uv sync --extra dev
```

### 开发工具

项目提供了多种开发工具来保证代码质量：

```bash
# 格式化代码
make format

# 代码检查
make lint

# 类型检查
make typecheck

# 运行测试
make test

# 运行所有检查
make check

# 启动开发服务器
make server
```

### 代码规范

- **格式化**: 使用 Black (line-length=88)
- **导入排序**: 使用 isort
- **代码检查**: 使用 flake8
- **类型检查**: 使用 mypy
- **测试**: 使用 pytest

### 项目结构

```
src/
├── config/          # 配置管理
├── models/          # 数据模型
├── services/        # 核心服务
├── storage/         # 存储管理
└── utils/           # 工具函数
```

## 📊 使用示例

### 获取Python编码规范
```python
# 通过MCP客户端调用
result = await mcp_client.call_tool("fetch_standard", {
    "domain": "coding_standards",
    "language": "python"
})
```

### 检查代码合规性
```python
code = """
def myFunction():
    userName = "John"
    return userName
"""

result = await mcp_client.call_tool("check_compliance", {
    "code": code,
    "language": "python"
})
```

### 搜索规范内容
```python
result = await mcp_client.call_tool("search_standard", {
    "query": "variable naming conventions",
    "language": "python",
    "max_results": 5
})
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如有问题或建议，请：

1. 查看 [Issues](../../issues) 页面
2. 创建新的 Issue
3. 联系项目维护者

---

**CodeStandardMCP** - 让编码规范管理变得简单高效！
