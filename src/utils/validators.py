"""
数据验证工具模块

提供规范数据格式验证、内容验证等功能
"""

import re
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import logging

from ..models.standard import StandardDocument, StandardRule, SeverityLevel

logger = logging.getLogger('CodeStandardMCP.Validators')


class ValidationError(Exception):
    """验证错误异常"""
    pass


class StandardValidator:
    """规范数据验证器"""
    
    def __init__(self):
        # 支持的编程语言
        self.supported_languages = {
            'python', 'javascript', 'java', 'csharp', 'cpp', 'c',
            'go', 'rust', 'php', 'ruby', 'swift', 'kotlin', 'typescript',
            'scala', 'r', 'matlab', 'shell', 'powershell'
        }
        
        # URL格式正则表达式
        self.url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    
    def validate_language(self, language: str) -> bool:
        """验证编程语言是否支持"""
        return language.lower() in self.supported_languages
    
    def validate_url(self, url: str) -> bool:
        """验证URL格式"""
        return bool(self.url_pattern.match(url))
    
    def validate_rule_id(self, rule_id: str) -> bool:
        """验证规则ID格式"""
        # 规则ID应该是字母数字和下划线的组合，长度在3-50之间
        pattern = r'^[a-zA-Z0-9_]{3,50}$'
        return bool(re.match(pattern, rule_id))
    
    def validate_severity(self, severity: str) -> bool:
        """验证严重程度"""
        try:
            SeverityLevel(severity)
            return True
        except ValueError:
            return False
    
    def validate_standard_rule(self, rule_data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证规范规则数据"""
        errors = []
        
        # 检查必需字段
        required_fields = ['id', 'title', 'description', 'severity']
        for field in required_fields:
            if field not in rule_data:
                errors.append(f"Missing required field: {field}")
            elif not rule_data[field]:
                errors.append(f"Empty required field: {field}")
        
        if errors:
            return False, errors
        
        # 验证规则ID
        if not self.validate_rule_id(rule_data['id']):
            errors.append(f"Invalid rule ID format: {rule_data['id']}")
        
        # 验证严重程度
        if not self.validate_severity(rule_data['severity']):
            errors.append(f"Invalid severity level: {rule_data['severity']}")
        
        # 验证标题长度
        if len(rule_data['title']) > 200:
            errors.append("Rule title too long (max 200 characters)")
        
        # 验证描述长度
        if len(rule_data['description']) > 2000:
            errors.append("Rule description too long (max 2000 characters)")
        
        # 验证示例数据
        if 'examples' in rule_data and isinstance(rule_data['examples'], list):
            for i, example in enumerate(rule_data['examples']):
                if not isinstance(example, dict):
                    errors.append(f"Example {i} must be an object")
                    continue
                
                if 'title' not in example:
                    errors.append(f"Example {i} missing title")
                
                if 'good_code' not in example and 'bad_code' not in example:
                    errors.append(f"Example {i} must have either good_code or bad_code")
        
        return len(errors) == 0, errors
    
    def validate_standard_document(self, document_data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证完整的规范文档数据"""
        errors = []
        
        # 检查顶级结构
        if 'metadata' not in document_data:
            errors.append("Missing metadata section")
            return False, errors
        
        # 验证元数据
        metadata = document_data['metadata']
        metadata_errors = self._validate_metadata(metadata)
        errors.extend(metadata_errors)
        
        # 验证章节
        if 'sections' in document_data:
            sections_errors = self._validate_sections(document_data['sections'])
            errors.extend(sections_errors)
        
        # 验证参考资料
        if 'references' in document_data:
            references_errors = self._validate_references(document_data['references'])
            errors.extend(references_errors)
        
        return len(errors) == 0, errors
    
    def _validate_metadata(self, metadata: Dict[str, Any]) -> List[str]:
        """验证元数据"""
        errors = []
        
        # 检查必需字段
        required_fields = ['language', 'version', 'last_updated', 'source', 'description']
        for field in required_fields:
            if field not in metadata:
                errors.append(f"Missing metadata field: {field}")
            elif not metadata[field]:
                errors.append(f"Empty metadata field: {field}")
        
        if errors:
            return errors
        
        # 验证语言
        if not self.validate_language(metadata['language']):
            errors.append(f"Unsupported language: {metadata['language']}")
        
        # 验证源URL
        if not self.validate_url(metadata['source']):
            errors.append(f"Invalid source URL: {metadata['source']}")
        
        # 验证日期格式
        try:
            datetime.fromisoformat(metadata['last_updated'].replace('Z', '+00:00'))
        except (ValueError, AttributeError):
            errors.append(f"Invalid date format in last_updated: {metadata['last_updated']}")
        
        # 验证版本格式
        version_pattern = r'^\d+\.\d+(\.\d+)?$'
        if not re.match(version_pattern, metadata['version']):
            errors.append(f"Invalid version format: {metadata['version']}")
        
        return errors
    
    def _validate_sections(self, sections: List[Dict[str, Any]]) -> List[str]:
        """验证章节数据"""
        errors = []
        
        if not isinstance(sections, list):
            errors.append("Sections must be a list")
            return errors
        
        for i, section in enumerate(sections):
            if not isinstance(section, dict):
                errors.append(f"Section {i} must be an object")
                continue
            
            # 检查必需字段
            if 'id' not in section:
                errors.append(f"Section {i} missing id")
            if 'title' not in section:
                errors.append(f"Section {i} missing title")
            
            # 验证规则
            if 'rules' in section and isinstance(section['rules'], list):
                for j, rule in enumerate(section['rules']):
                    is_valid, rule_errors = self.validate_standard_rule(rule)
                    if not is_valid:
                        for error in rule_errors:
                            errors.append(f"Section {i}, Rule {j}: {error}")
            
            # 递归验证子章节
            if 'subsections' in section and isinstance(section['subsections'], list):
                subsection_errors = self._validate_sections(section['subsections'])
                for error in subsection_errors:
                    errors.append(f"Section {i}, {error}")
        
        return errors
    
    def _validate_references(self, references: List[Dict[str, Any]]) -> List[str]:
        """验证参考资料"""
        errors = []
        
        if not isinstance(references, list):
            errors.append("References must be a list")
            return errors
        
        for i, reference in enumerate(references):
            if not isinstance(reference, dict):
                errors.append(f"Reference {i} must be an object")
                continue
            
            # 检查必需字段
            if 'title' not in reference:
                errors.append(f"Reference {i} missing title")
            if 'url' not in reference:
                errors.append(f"Reference {i} missing url")
            elif not self.validate_url(reference['url']):
                errors.append(f"Reference {i} has invalid URL: {reference['url']}")
        
        return errors
    
    def validate_code_snippet(self, code: str, max_length: int = 10000) -> Tuple[bool, List[str]]:
        """验证代码片段"""
        errors = []
        
        if not isinstance(code, str):
            errors.append("Code must be a string")
            return False, errors
        
        if len(code) > max_length:
            errors.append(f"Code too long (max {max_length} characters)")
        
        if not code.strip():
            errors.append("Code cannot be empty")
        
        return len(errors) == 0, errors
    
    def validate_search_query(self, query: str) -> Tuple[bool, List[str]]:
        """验证搜索查询"""
        errors = []
        
        if not isinstance(query, str):
            errors.append("Query must be a string")
            return False, errors
        
        if len(query.strip()) < 2:
            errors.append("Query too short (minimum 2 characters)")
        
        if len(query) > 200:
            errors.append("Query too long (max 200 characters)")
        
        return len(errors) == 0, errors


# 全局验证器实例
validator = StandardValidator()
