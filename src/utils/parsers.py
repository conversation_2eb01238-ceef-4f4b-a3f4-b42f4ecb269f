"""
内容解析工具模块

提供规范内容解析、代码分析等功能
"""

import re
import json
from typing import Dict, Any, List, Optional, Tuple
import logging

logger = logging.getLogger('CodeStandardMCP.Parsers')


class ContentParser:
    """内容解析器类"""
    
    def __init__(self):
        # 代码语言检测模式
        self.language_patterns = {
            'python': [
                r'def\s+\w+\s*\(',
                r'import\s+\w+',
                r'from\s+\w+\s+import',
                r'class\s+\w+\s*\(',
                r'if\s+__name__\s*==\s*["\']__main__["\']',
            ],
            'javascript': [
                r'function\s+\w+\s*\(',
                r'const\s+\w+\s*=',
                r'let\s+\w+\s*=',
                r'var\s+\w+\s*=',
                r'=>\s*{',
                r'require\s*\(',
            ],
            'java': [
                r'public\s+class\s+\w+',
                r'private\s+\w+\s+\w+',
                r'public\s+static\s+void\s+main',
                r'import\s+java\.',
                r'@\w+',
            ],
            'csharp': [
                r'public\s+class\s+\w+',
                r'using\s+System',
                r'namespace\s+\w+',
                r'public\s+static\s+void\s+Main',
                r'\[.*\]',
            ],
            'go': [
                r'package\s+\w+',
                r'func\s+\w+\s*\(',
                r'import\s*\(',
                r'var\s+\w+\s+\w+',
                r'type\s+\w+\s+struct',
            ],
        }
    
    def detect_code_language(self, code: str) -> Optional[str]:
        """检测代码语言"""
        code = code.strip()
        if not code:
            return None
        
        language_scores = {}
        
        for language, patterns in self.language_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, code, re.MULTILINE | re.IGNORECASE))
                score += matches
            
            if score > 0:
                language_scores[language] = score
        
        if language_scores:
            # 返回得分最高的语言
            return max(language_scores, key=language_scores.get)
        
        return None
    
    def extract_code_blocks(self, text: str) -> List[Dict[str, Any]]:
        """从文本中提取代码块"""
        code_blocks = []
        
        # 匹配Markdown代码块
        markdown_pattern = r'```(\w+)?\n(.*?)\n```'
        matches = re.finditer(markdown_pattern, text, re.DOTALL)
        
        for match in matches:
            language = match.group(1) or self.detect_code_language(match.group(2))
            code = match.group(2).strip()
            
            if code:
                code_blocks.append({
                    'language': language,
                    'code': code,
                    'start_pos': match.start(),
                    'end_pos': match.end(),
                })
        
        return code_blocks
    
    def parse_standard_content(self, content: str, source_format: str = 'auto') -> Dict[str, Any]:
        """解析规范内容"""
        try:
            if source_format == 'json' or (source_format == 'auto' and content.strip().startswith('{')):
                return self._parse_json_content(content)
            elif source_format == 'markdown' or (source_format == 'auto' and '# ' in content):
                return self._parse_markdown_content(content)
            else:
                return self._parse_plain_text_content(content)
        except Exception as e:
            logger.error(f"Error parsing standard content: {e}")
            return {"error": str(e)}
    
    def _parse_json_content(self, content: str) -> Dict[str, Any]:
        """解析JSON格式的规范内容"""
        try:
            data = json.loads(content)
            return {
                "format": "json",
                "data": data,
                "success": True
            }
        except json.JSONDecodeError as e:
            return {
                "format": "json",
                "error": f"Invalid JSON: {str(e)}",
                "success": False
            }
    
    def _parse_markdown_content(self, content: str) -> Dict[str, Any]:
        """解析Markdown格式的规范内容"""
        sections = []
        current_section = None
        current_subsection = None
        
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            
            # 一级标题（章节）
            if line.startswith('# '):
                if current_section:
                    sections.append(current_section)
                
                current_section = {
                    'title': line[2:].strip(),
                    'content': [],
                    'subsections': [],
                    'code_examples': []
                }
                current_subsection = None
            
            # 二级标题（子章节）
            elif line.startswith('## '):
                if current_section:
                    if current_subsection:
                        current_section['subsections'].append(current_subsection)
                    
                    current_subsection = {
                        'title': line[3:].strip(),
                        'content': [],
                        'code_examples': []
                    }
            
            # 代码块
            elif line.startswith('```'):
                # 处理代码块（简化版）
                continue
            
            # 普通内容
            elif line:
                if current_subsection:
                    current_subsection['content'].append(line)
                elif current_section:
                    current_section['content'].append(line)
        
        # 添加最后一个章节
        if current_section:
            if current_subsection:
                current_section['subsections'].append(current_subsection)
            sections.append(current_section)
        
        return {
            "format": "markdown",
            "sections": sections,
            "success": True
        }
    
    def _parse_plain_text_content(self, content: str) -> Dict[str, Any]:
        """解析纯文本格式的规范内容"""
        # 简单的文本解析
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        
        return {
            "format": "plain_text",
            "paragraphs": paragraphs,
            "success": True
        }
    
    def extract_rules_from_content(self, parsed_content: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从解析的内容中提取规则"""
        rules = []
        
        if parsed_content.get("format") == "json":
            # 从JSON数据中提取规则
            data = parsed_content.get("data", {})
            if "sections" in data:
                for section in data["sections"]:
                    if "rules" in section:
                        rules.extend(section["rules"])
        
        elif parsed_content.get("format") == "markdown":
            # 从Markdown内容中提取规则
            sections = parsed_content.get("sections", [])
            for section in sections:
                # 简化的规则提取逻辑
                content_text = ' '.join(section.get("content", []))
                if any(keyword in content_text.lower() for keyword in ['rule', 'should', 'must', 'avoid']):
                    rule = {
                        "id": f"rule_{len(rules) + 1}",
                        "title": section.get("title", "Untitled Rule"),
                        "description": content_text,
                        "severity": "medium",
                        "source_section": section.get("title")
                    }
                    rules.append(rule)
        
        return rules
    
    def search_in_content(self, content: str, query: str, context_lines: int = 3) -> List[Dict[str, Any]]:
        """在内容中搜索关键词"""
        results = []
        lines = content.split('\n')
        query_lower = query.lower()
        
        for i, line in enumerate(lines):
            if query_lower in line.lower():
                # 获取上下文
                start_line = max(0, i - context_lines)
                end_line = min(len(lines), i + context_lines + 1)
                
                context = lines[start_line:end_line]
                
                result = {
                    "line_number": i + 1,
                    "matched_line": line.strip(),
                    "context": context,
                    "start_line": start_line + 1,
                    "end_line": end_line,
                }
                results.append(result)
        
        return results
    
    def extract_keywords(self, text: str) -> List[str]:
        """从文本中提取关键词"""
        # 简单的关键词提取
        # 移除标点符号，转换为小写，分割单词
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
        
        # 过滤常见停用词
        stop_words = {
            'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before',
            'after', 'above', 'below', 'between', 'among', 'this', 'that', 'these',
            'those', 'you', 'your', 'they', 'their', 'them', 'should', 'would',
            'could', 'will', 'can', 'may', 'might', 'must', 'shall'
        }
        
        keywords = [word for word in set(words) if word not in stop_words and len(word) > 3]
        
        # 按频率排序
        word_freq = {}
        for word in words:
            if word not in stop_words and len(word) > 3:
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # 返回按频率排序的关键词
        sorted_keywords = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return [word for word, freq in sorted_keywords[:20]]  # 返回前20个关键词
    
    def normalize_text(self, text: str) -> str:
        """标准化文本"""
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除首尾空白
        text = text.strip()
        
        return text
    
    def split_into_sentences(self, text: str) -> List[str]:
        """将文本分割为句子"""
        # 简单的句子分割
        sentences = re.split(r'[.!?]+', text)
        return [s.strip() for s in sentences if s.strip()]


# 全局解析器实例
parser = ContentParser()
