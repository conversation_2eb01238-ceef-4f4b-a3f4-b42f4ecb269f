"""
编码规范浏览和搜索服务

实现规范列表、搜索、规则详情查询等功能
"""

from typing import Dict, Any, List, Optional
import logging

from ..models.standard import StandardDocument, StandardRule
from ..config.settings import config
from ..storage.file_manager import file_manager
from ..storage.index_manager import index_manager
from ..utils.validators import validator
from ..utils.parsers import parser

logger = logging.getLogger('CodeStandardMCP.StandardExplorer')


class StandardExplorer:
    """编码规范浏览和搜索服务类"""
    
    def __init__(self):
        self.context_lines = config.context_lines
    
    async def list_standards(self, language: Optional[str] = None, 
                           framework: Optional[str] = None) -> Dict[str, Any]:
        """
        列出编码规范
        
        Args:
            language: 可选的语言筛选
            framework: 可选的框架筛选
        
        Returns:
            包含规范列表的字典
        """
        try:
            logger.info(f"Listing standards - language: {language}, framework: {framework}")
            
            # 验证输入参数
            if language and not validator.validate_language(language):
                return {
                    "success": False,
                    "error": f"Unsupported language: {language}",
                    "error_type": "validation_error"
                }
            
            # 从索引获取规范列表
            standards = await index_manager.list_all_standards(language, framework)
            
            # 格式化结果
            formatted_standards = []
            for standard in standards:
                formatted_standard = {
                    "language": standard["language"],
                    "framework": standard.get("framework"),
                    "version": standard["version"],
                    "description": standard["description"],
                    "last_updated": standard["last_updated"],
                    "rule_count": standard["rule_count"],
                    "source": standard["source"],
                    "file_path": standard["path"]
                }
                formatted_standards.append(formatted_standard)
            
            return {
                "success": True,
                "standards": formatted_standards,
                "total_count": len(formatted_standards),
                "filters": {
                    "language": language,
                    "framework": framework
                }
            }
            
        except Exception as e:
            logger.error(f"Error listing standards: {e}")
            return {
                "success": False,
                "error": str(e),
                "error_type": "unexpected_error"
            }
    
    async def search_standard(self, query: str, language: Optional[str] = None,
                            framework: Optional[str] = None, 
                            context_lines: Optional[int] = None) -> Dict[str, Any]:
        """
        搜索编码规范
        
        Args:
            query: 搜索关键词
            language: 可选的编程语言筛选
            framework: 可选的框架筛选
            context_lines: 返回匹配内容周围的上下文行数
        
        Returns:
            包含搜索结果的字典
        """
        try:
            # 验证搜索查询
            is_valid, errors = validator.validate_search_query(query)
            if not is_valid:
                return {
                    "success": False,
                    "error": f"Invalid search query: {', '.join(errors)}",
                    "error_type": "validation_error"
                }
            
            if context_lines is None:
                context_lines = self.context_lines
            
            logger.info(f"Searching standards for: '{query}' in {language}/{framework}")
            
            # 获取要搜索的规范列表
            standards = await index_manager.list_all_standards(language, framework)
            
            search_results = []
            
            for standard_info in standards:
                # 加载规范文档
                document = await self._load_standard_document(
                    standard_info["language"], 
                    standard_info.get("framework")
                )
                
                if not document:
                    continue
                
                # 在规范中搜索
                matches = await self._search_in_document(document, query, context_lines)
                
                if matches:
                    search_results.append({
                        "standard": {
                            "language": standard_info["language"],
                            "framework": standard_info.get("framework"),
                            "version": standard_info["version"],
                            "description": standard_info["description"]
                        },
                        "matches": matches,
                        "match_count": len(matches)
                    })
            
            # 按匹配数量排序
            search_results.sort(key=lambda x: x["match_count"], reverse=True)
            
            return {
                "success": True,
                "query": query,
                "results": search_results,
                "total_matches": sum(r["match_count"] for r in search_results),
                "total_standards_searched": len(standards),
                "filters": {
                    "language": language,
                    "framework": framework,
                    "context_lines": context_lines
                }
            }
            
        except Exception as e:
            logger.error(f"Error searching standards: {e}")
            return {
                "success": False,
                "error": str(e),
                "error_type": "unexpected_error"
            }
    
    async def get_rule_details(self, rule_id: str, language: str, 
                             framework: Optional[str] = None) -> Dict[str, Any]:
        """
        获取规则详情
        
        Args:
            rule_id: 规则标识符
            language: 编程语言
            framework: 可选的框架名称
        
        Returns:
            包含规则详情的字典
        """
        try:
            # 验证输入参数
            if not validator.validate_rule_id(rule_id):
                return {
                    "success": False,
                    "error": f"Invalid rule ID format: {rule_id}",
                    "error_type": "validation_error"
                }
            
            if not validator.validate_language(language):
                return {
                    "success": False,
                    "error": f"Unsupported language: {language}",
                    "error_type": "validation_error"
                }
            
            logger.info(f"Getting rule details: {rule_id} for {language}/{framework}")
            
            # 加载规范文档
            document = await self._load_standard_document(language, framework)
            if not document:
                return {
                    "success": False,
                    "error": f"Standard not found for {language}/{framework}",
                    "error_type": "not_found"
                }
            
            # 查找规则
            rule = document.find_rule_by_id(rule_id)
            if not rule:
                return {
                    "success": False,
                    "error": f"Rule not found: {rule_id}",
                    "error_type": "not_found"
                }
            
            # 获取相关规则（同一章节的其他规则）
            related_rules = await self._get_related_rules(document, rule)
            
            return {
                "success": True,
                "rule": rule.to_dict(),
                "standard_info": {
                    "language": document.metadata.language,
                    "framework": document.metadata.framework,
                    "version": document.metadata.version,
                    "source": document.metadata.source
                },
                "related_rules": [r.to_dict() for r in related_rules[:5]],  # 最多5个相关规则
                "total_rules_in_standard": len(document.get_all_rules())
            }
            
        except Exception as e:
            logger.error(f"Error getting rule details: {e}")
            return {
                "success": False,
                "error": str(e),
                "error_type": "unexpected_error"
            }
    
    async def get_standards_summary(self) -> Dict[str, Any]:
        """获取规范概览信息"""
        try:
            # 获取索引统计
            index_stats = await index_manager.get_index_stats()
            
            # 获取所有语言和框架
            languages = await index_manager.list_languages()
            
            language_summary = {}
            for language in languages:
                frameworks = await index_manager.list_frameworks(language)
                language_summary[language] = {
                    "frameworks": frameworks,
                    "framework_count": len(frameworks)
                }
            
            return {
                "success": True,
                "summary": {
                    "total_languages": len(languages),
                    "total_standards": index_stats.get("total_standards", 0),
                    "last_updated": index_stats.get("last_updated"),
                    "languages": language_summary
                },
                "index_stats": index_stats
            }
            
        except Exception as e:
            logger.error(f"Error getting standards summary: {e}")
            return {
                "success": False,
                "error": str(e),
                "error_type": "unexpected_error"
            }
    
    async def _load_standard_document(self, language: str, 
                                    framework: Optional[str] = None) -> Optional[StandardDocument]:
        """加载规范文档"""
        try:
            file_path = config.get_standard_file_path(language, framework)
            document_data = await file_manager.read_json(file_path)
            
            if document_data:
                return StandardDocument.from_dict(document_data)
            
            return None
            
        except Exception as e:
            logger.error(f"Error loading standard document: {e}")
            return None
    
    async def _search_in_document(self, document: StandardDocument, query: str, 
                                context_lines: int) -> List[Dict[str, Any]]:
        """在文档中搜索"""
        matches = []
        
        # 在规则中搜索
        for rule in document.get_all_rules():
            rule_matches = self._search_in_rule(rule, query, context_lines)
            if rule_matches:
                matches.extend(rule_matches)
        
        return matches
    
    def _search_in_rule(self, rule: StandardRule, query: str, context_lines: int) -> List[Dict[str, Any]]:
        """在规则中搜索"""
        matches = []
        query_lower = query.lower()
        
        # 搜索标题
        if query_lower in rule.title.lower():
            matches.append({
                "type": "title",
                "rule_id": rule.id,
                "rule_title": rule.title,
                "matched_text": rule.title,
                "context": rule.description[:200] + "..." if len(rule.description) > 200 else rule.description,
                "severity": rule.severity.value
            })
        
        # 搜索描述
        if query_lower in rule.description.lower():
            # 找到匹配位置并提取上下文
            description_lines = rule.description.split('\n')
            for i, line in enumerate(description_lines):
                if query_lower in line.lower():
                    start_line = max(0, i - context_lines)
                    end_line = min(len(description_lines), i + context_lines + 1)
                    context = '\n'.join(description_lines[start_line:end_line])
                    
                    matches.append({
                        "type": "description",
                        "rule_id": rule.id,
                        "rule_title": rule.title,
                        "matched_text": line.strip(),
                        "context": context,
                        "severity": rule.severity.value,
                        "line_number": i + 1
                    })
        
        # 搜索标签
        for tag in rule.tags:
            if query_lower in tag.lower():
                matches.append({
                    "type": "tag",
                    "rule_id": rule.id,
                    "rule_title": rule.title,
                    "matched_text": tag,
                    "context": rule.description[:100] + "..." if len(rule.description) > 100 else rule.description,
                    "severity": rule.severity.value
                })
        
        # 搜索示例
        for example in rule.examples:
            if (example.good_code and query_lower in example.good_code.lower()) or \
               (example.bad_code and query_lower in example.bad_code.lower()) or \
               (example.explanation and query_lower in example.explanation.lower()):
                matches.append({
                    "type": "example",
                    "rule_id": rule.id,
                    "rule_title": rule.title,
                    "matched_text": example.title,
                    "context": example.explanation or "Code example",
                    "severity": rule.severity.value,
                    "example_title": example.title
                })
        
        return matches
    
    async def _get_related_rules(self, document: StandardDocument, target_rule: StandardRule) -> List[StandardRule]:
        """获取相关规则"""
        related_rules = []
        
        # 找到目标规则所在的章节
        for section in document.sections:
            if target_rule in section.get_all_rules():
                # 获取同一章节的其他规则
                section_rules = section.get_all_rules()
                related_rules.extend([r for r in section_rules if r.id != target_rule.id])
                break
        
        # 如果同章节规则不够，按标签相似度查找
        if len(related_rules) < 5:
            all_rules = document.get_all_rules()
            for rule in all_rules:
                if rule.id != target_rule.id and rule not in related_rules:
                    # 检查是否有共同标签
                    common_tags = set(rule.tags) & set(target_rule.tags)
                    if common_tags:
                        related_rules.append(rule)
                        if len(related_rules) >= 5:
                            break
        
        return related_rules


# 全局规范浏览器实例
standard_explorer = StandardExplorer()
