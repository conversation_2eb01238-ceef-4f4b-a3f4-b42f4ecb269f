"""
编码规范获取服务

实现从外部API获取编码规范并缓存到本地的功能
"""

import logging
from datetime import datetime
from typing import Any, Dict, Optional

import httpx

from ..config.settings import config
from ..models.standard import StandardDocument, StandardMetadata
from ..storage.cache_manager import cache_manager
from ..storage.file_manager import file_manager
from ..storage.index_manager import index_manager
from ..utils.parsers import parser
from ..utils.validators import validator

logger = logging.getLogger("CodeStandardMCP.StandardFetcher")


class StandardFetcher:
    """编码规范获取服务类"""

    def __init__(self):
        self.http_timeout = config.http_timeout
        self.user_agent = config.user_agent
        self.max_retries = config.max_retries

    async def fetch_standard(
        self,
        domain: str,
        language: str,
        framework: Optional[str] = None,
        force_refresh: bool = False,
    ) -> Dict[str, Any]:
        """
        获取编码规范

        Args:
            domain: 规范领域描述
            language: 编程语言
            framework: 框架名称（可选）
            force_refresh: 是否强制刷新缓存

        Returns:
            包含规范数据或错误信息的字典
        """
        try:
            # 验证输入参数
            if not validator.validate_language(language):
                return {
                    "success": False,
                    "error": f"Unsupported language: {language}",
                    "error_type": "validation_error",
                }

            logger.info(
                f"Fetching standard for {language}"
                + (f"/{framework}" if framework else "")
                + f" in domain: {domain}"
            )

            # 检查本地缓存
            if not force_refresh:
                cached_standard = await self._get_cached_standard(language, framework)
                if cached_standard:
                    logger.info(f"Using cached standard for {language}/{framework}")
                    return {
                        "success": True,
                        "data": cached_standard,
                        "source": "cache",
                        "language": language,
                        "framework": framework,
                    }

            # 从远程API获取规范
            api_url = self._build_api_url(domain, language, framework)
            if not api_url:
                return {
                    "success": False,
                    "error": f"No API endpoint configured for {language}/{framework}",
                    "error_type": "configuration_error",
                }

            # 获取远程内容
            remote_content = await self._fetch_from_api(api_url, domain)
            if not remote_content["success"]:
                return remote_content

            # 解析和验证内容
            standard_document = await self._process_remote_content(
                remote_content["content"], language, framework, api_url
            )

            if not standard_document:
                return {
                    "success": False,
                    "error": "Failed to process remote content",
                    "error_type": "processing_error",
                }

            # 保存到本地存储
            save_success = await self._save_standard(
                standard_document, language, framework
            )
            if not save_success:
                logger.warning("Failed to save standard to local storage")

            return {
                "success": True,
                "data": standard_document.to_dict(),
                "source": "network",
                "language": language,
                "framework": framework,
                "api_url": api_url,
            }

        except Exception as e:
            logger.error(f"Error fetching standard: {e}")
            return {"success": False, "error": str(e), "error_type": "unexpected_error"}

    def _build_api_url(
        self, domain: str, language: str, framework: Optional[str] = None
    ) -> Optional[str]:
        """构建API请求URL"""
        base_url = config.get_api_endpoint(language, framework)
        if not base_url:
            return None

        # 添加查询参数
        params = {"language": language, "domain": domain}

        if framework:
            params["framework"] = framework

        # 构建完整URL
        param_string = "&".join([f"{k}={v}" for k, v in params.items()])
        return f"{base_url}?{param_string}"

    async def _fetch_from_api(self, url: str, domain: str) -> Dict[str, Any]:
        """从API获取内容"""
        try:
            # 使用缓存管理器获取内容
            cached_content = await cache_manager.get_cached_content(url, domain)
            if cached_content:
                return {"success": True, "content": cached_content, "source": "cache"}

            # 发起HTTP请求
            async with httpx.AsyncClient(
                timeout=self.http_timeout, headers={"User-Agent": self.user_agent}
            ) as client:

                # 尝试GET请求
                response = await self._try_http_request(client, url, "GET")
                if response["success"]:
                    content = response["content"]
                else:
                    # 尝试POST请求
                    response = await self._try_http_request(client, url, "POST")
                    if not response["success"]:
                        return response
                    content = response["content"]

                # 缓存内容
                await cache_manager.store_cached_content(url, content, domain, "text")

                return {"success": True, "content": content, "source": "network"}

        except Exception as e:
            logger.error(f"Error fetching from API {url}: {e}")
            return {"success": False, "error": str(e), "error_type": "network_error"}

    async def _try_http_request(
        self, client: httpx.AsyncClient, url: str, method: str
    ) -> Dict[str, Any]:
        """尝试HTTP请求"""
        try:
            if method.upper() == "GET":
                response = await client.get(url)
            elif method.upper() == "POST":
                response = await client.post(url)
            else:
                return {"success": False, "error": f"Unsupported HTTP method: {method}"}

            response.raise_for_status()

            return {
                "success": True,
                "content": response.text,
                "status_code": response.status_code,
                "method": method.upper(),
            }

        except httpx.HTTPStatusError as e:
            return {
                "success": False,
                "error": f"HTTP {e.response.status_code}: {e.response.reason_phrase}",
                "status_code": e.response.status_code,
                "method": method.upper(),
            }
        except httpx.TimeoutException:
            return {
                "success": False,
                "error": f"Request timeout after {self.http_timeout}s",
                "method": method.upper(),
            }
        except Exception as e:
            return {"success": False, "error": str(e), "method": method.upper()}

    async def _process_remote_content(
        self, content: str, language: str, framework: Optional[str], source_url: str
    ) -> Optional[StandardDocument]:
        """处理远程获取的内容"""
        try:
            # 解析内容
            parsed_content = parser.parse_standard_content(content)
            if not parsed_content.get("success"):
                logger.error(f"Failed to parse content: {parsed_content.get('error')}")
                return None

            # 根据解析结果创建标准文档
            if parsed_content["format"] == "json":
                # 如果是JSON格式，直接验证和创建
                document_data = parsed_content["data"]
                is_valid, errors = validator.validate_standard_document(document_data)
                if not is_valid:
                    logger.error(f"Invalid standard document: {errors}")
                    return None

                return StandardDocument.from_dict(document_data)

            else:
                # 如果是其他格式，创建基本的标准文档结构
                return await self._create_standard_from_parsed_content(
                    parsed_content, language, framework, source_url
                )

        except Exception as e:
            logger.error(f"Error processing remote content: {e}")
            return None

    async def _create_standard_from_parsed_content(
        self,
        parsed_content: Dict[str, Any],
        language: str,
        framework: Optional[str],
        source_url: str,
    ) -> StandardDocument:
        """从解析的内容创建标准文档"""
        # 创建元数据
        metadata = StandardMetadata(
            language=language,
            framework=framework,
            version="1.0.0",
            last_updated=datetime.now(),
            source=source_url,
            description=f"Coding standards for {language}"
            + (f" with {framework}" if framework else ""),
            author="External API",
            license="Unknown",
        )

        # 创建标准文档
        document = StandardDocument(metadata=metadata, sections=[], references=[])

        # 从解析内容中提取规则
        rules = parser.extract_rules_from_content(parsed_content)
        if rules:
            # 创建一个默认章节包含所有规则
            from ..models.standard import StandardRule, StandardSection

            section_rules = []
            for rule_data in rules:
                try:
                    rule = StandardRule(**rule_data)
                    section_rules.append(rule)
                except Exception as e:
                    logger.warning(f"Failed to create rule from data: {e}")

            if section_rules:
                section = StandardSection(
                    id="general_rules",
                    title="General Coding Rules",
                    description="General coding standards and best practices",
                    rules=section_rules,
                )
                document.sections.append(section)

        return document

    async def _get_cached_standard(
        self, language: str, framework: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """获取缓存的规范"""
        try:
            # 检查索引中是否存在
            standard_info = await index_manager.get_standard_info(language, framework)
            if not standard_info:
                return None

            # 读取规范文件
            file_path = config.get_standard_file_path(language, framework)
            document_data = await file_manager.read_json(file_path)

            if document_data:
                # 验证数据
                is_valid, errors = validator.validate_standard_document(document_data)
                if is_valid:
                    return document_data
                else:
                    logger.warning(f"Cached standard validation failed: {errors}")

            return None

        except Exception as e:
            logger.error(f"Error getting cached standard: {e}")
            return None

    async def _save_standard(
        self, document: StandardDocument, language: str, framework: Optional[str] = None
    ) -> bool:
        """保存规范到本地存储"""
        try:
            # 保存规范文件
            file_path = config.get_standard_file_path(language, framework)
            document_data = document.to_dict()

            save_success = await file_manager.write_json(file_path, document_data)
            if not save_success:
                return False

            # 更新索引
            index_success = await index_manager.add_standard(document, str(file_path))
            if not index_success:
                logger.warning("Failed to update index")

            logger.info(f"Standard saved successfully: {language}/{framework}")
            return True

        except Exception as e:
            logger.error(f"Error saving standard: {e}")
            return False


# 全局规范获取器实例
standard_fetcher = StandardFetcher()
