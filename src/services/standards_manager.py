"""
编码规范管理服务

实现规范更新、验证、缓存清理等管理功能
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import logging

from ..models.standard import StandardDocument
from ..config.settings import config
from ..storage.file_manager import file_manager
from ..storage.cache_manager import cache_manager
from ..storage.index_manager import index_manager
from ..utils.validators import validator
from ..services.standard_fetcher import standard_fetcher

logger = logging.getLogger('CodeStandardMCP.StandardsManager')


class StandardsManager:
    """编码规范管理服务类"""
    
    def __init__(self):
        self.auto_update_interval = timedelta(days=config.auto_update_days)
    
    async def update_standards(self, language: Optional[str] = None, 
                             framework: Optional[str] = None,
                             force_update: bool = False) -> Dict[str, Any]:
        """
        更新编码规范
        
        Args:
            language: 可选的特定语言
            framework: 可选的特定框架
            force_update: 是否强制更新
        
        Returns:
            包含更新结果的字典
        """
        try:
            logger.info(f"Updating standards - language: {language}, framework: {framework}, force: {force_update}")
            
            # 验证输入参数
            if language and not validator.validate_language(language):
                return {
                    "success": False,
                    "error": f"Unsupported language: {language}",
                    "error_type": "validation_error"
                }
            
            # 获取需要更新的规范列表
            standards_to_update = await self._get_standards_to_update(language, framework, force_update)
            
            if not standards_to_update:
                return {
                    "success": True,
                    "message": "No standards need updating",
                    "updated_count": 0,
                    "skipped_count": 0,
                    "failed_count": 0
                }
            
            # 执行更新
            update_results = []
            updated_count = 0
            failed_count = 0
            
            for standard_info in standards_to_update:
                result = await self._update_single_standard(standard_info, force_update)
                update_results.append(result)
                
                if result["success"]:
                    updated_count += 1
                else:
                    failed_count += 1
            
            return {
                "success": True,
                "updated_count": updated_count,
                "failed_count": failed_count,
                "total_checked": len(standards_to_update),
                "results": update_results
            }
            
        except Exception as e:
            logger.error(f"Error updating standards: {e}")
            return {
                "success": False,
                "error": str(e),
                "error_type": "unexpected_error"
            }
    
    async def validate_standard(self, language: str, framework: Optional[str] = None) -> Dict[str, Any]:
        """
        验证规范文档
        
        Args:
            language: 编程语言
            framework: 可选的框架名称
        
        Returns:
            包含验证结果的字典
        """
        try:
            # 验证输入参数
            if not validator.validate_language(language):
                return {
                    "success": False,
                    "error": f"Unsupported language: {language}",
                    "error_type": "validation_error"
                }
            
            logger.info(f"Validating standard for {language}/{framework}")
            
            # 加载规范文档
            file_path = config.get_standard_file_path(language, framework)
            document_data = await file_manager.read_json(file_path)
            
            if not document_data:
                return {
                    "success": False,
                    "error": f"Standard file not found: {file_path}",
                    "error_type": "not_found"
                }
            
            # 验证文档结构
            is_valid, errors = validator.validate_standard_document(document_data)
            
            if is_valid:
                # 创建文档对象进行进一步验证
                try:
                    document = StandardDocument.from_dict(document_data)
                    
                    # 获取文档统计信息
                    stats = {
                        "total_sections": len(document.sections),
                        "total_rules": len(document.get_all_rules()),
                        "severity_breakdown": self._get_severity_breakdown(document),
                        "last_updated": document.metadata.last_updated.isoformat(),
                        "version": document.metadata.version
                    }
                    
                    return {
                        "success": True,
                        "valid": True,
                        "errors": [],
                        "warnings": [],
                        "statistics": stats,
                        "file_path": str(file_path)
                    }
                    
                except Exception as e:
                    return {
                        "success": True,
                        "valid": False,
                        "errors": [f"Failed to create document object: {str(e)}"],
                        "warnings": [],
                        "file_path": str(file_path)
                    }
            else:
                return {
                    "success": True,
                    "valid": False,
                    "errors": errors,
                    "warnings": [],
                    "file_path": str(file_path)
                }
                
        except Exception as e:
            logger.error(f"Error validating standard: {e}")
            return {
                "success": False,
                "error": str(e),
                "error_type": "unexpected_error"
            }
    
    async def clean_cache(self, max_age_days: Optional[int] = None,
                         max_size_mb: Optional[int] = None) -> Dict[str, Any]:
        """
        清理缓存
        
        Args:
            max_age_days: 最大缓存天数
            max_size_mb: 最大缓存大小（MB）
        
        Returns:
            包含清理结果的字典
        """
        try:
            logger.info(f"Cleaning cache - max_age: {max_age_days} days, max_size: {max_size_mb} MB")
            
            # 获取清理前的缓存统计
            before_stats = await cache_manager.get_cache_stats()
            
            # 执行清理操作
            cleanup_results = []
            
            # 清理过期缓存
            if max_age_days is not None:
                expired_result = await cache_manager.cleanup_expired_cache(max_age_days)
                cleanup_results.append({
                    "type": "expired",
                    "removed_count": expired_result.get("removed_count", 0),
                    "freed_space": expired_result.get("freed_space", 0)
                })
            
            # 清理未使用的缓存
            unused_result = await cache_manager.cleanup_unused_cache()
            cleanup_results.append({
                "type": "unused",
                "removed_count": unused_result.get("removed_count", 0),
                "freed_space": unused_result.get("freed_space", 0)
            })
            
            # 清理超大缓存
            if max_size_mb is not None:
                oversized_result = await cache_manager.cleanup_oversized_cache(max_size_mb * 1024 * 1024)
                cleanup_results.append({
                    "type": "oversized",
                    "removed_count": oversized_result.get("removed_count", 0),
                    "freed_space": oversized_result.get("freed_space", 0)
                })
            
            # 获取清理后的缓存统计
            after_stats = await cache_manager.get_cache_stats()
            
            # 计算总的清理效果
            total_removed = sum(r["removed_count"] for r in cleanup_results)
            total_freed = sum(r["freed_space"] for r in cleanup_results)
            
            return {
                "success": True,
                "cleanup_summary": {
                    "total_removed_entries": total_removed,
                    "total_freed_space": total_freed,
                    "before_stats": before_stats,
                    "after_stats": after_stats
                },
                "cleanup_details": cleanup_results
            }
            
        except Exception as e:
            logger.error(f"Error cleaning cache: {e}")
            return {
                "success": False,
                "error": str(e),
                "error_type": "unexpected_error"
            }
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            # 获取索引统计
            index_stats = await index_manager.get_index_stats()
            
            # 获取缓存统计
            cache_stats = await cache_manager.get_cache_stats()
            
            # 获取存储统计
            storage_stats = await self._get_storage_stats()
            
            # 检查系统健康状态
            health_status = await self._check_system_health()
            
            return {
                "success": True,
                "system_status": {
                    "index": index_stats,
                    "cache": cache_stats,
                    "storage": storage_stats,
                    "health": health_status,
                    "timestamp": datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {
                "success": False,
                "error": str(e),
                "error_type": "unexpected_error"
            }
    
    async def _get_standards_to_update(self, language: Optional[str], 
                                     framework: Optional[str], 
                                     force_update: bool) -> List[Dict[str, Any]]:
        """获取需要更新的规范列表"""
        if force_update:
            # 强制更新时，获取所有匹配的规范
            return await index_manager.list_all_standards(language, framework)
        
        # 检查哪些规范需要更新
        all_standards = await index_manager.list_all_standards(language, framework)
        standards_to_update = []
        
        for standard in all_standards:
            # 检查是否需要更新
            last_updated = datetime.fromisoformat(standard["last_updated"].replace('Z', '+00:00'))
            if datetime.now() - last_updated > self.auto_update_interval:
                standards_to_update.append(standard)
        
        return standards_to_update
    
    async def _update_single_standard(self, standard_info: Dict[str, Any], 
                                    force_update: bool) -> Dict[str, Any]:
        """更新单个规范"""
        try:
            language = standard_info["language"]
            framework = standard_info.get("framework")
            
            # 使用StandardFetcher获取最新规范
            result = await standard_fetcher.fetch_standard(
                domain="coding_standards",
                language=language,
                framework=framework,
                force_refresh=force_update
            )
            
            if result["success"]:
                return {
                    "success": True,
                    "language": language,
                    "framework": framework,
                    "source": result["source"],
                    "message": "Standard updated successfully"
                }
            else:
                return {
                    "success": False,
                    "language": language,
                    "framework": framework,
                    "error": result["error"],
                    "error_type": result.get("error_type", "unknown")
                }
                
        except Exception as e:
            return {
                "success": False,
                "language": standard_info["language"],
                "framework": standard_info.get("framework"),
                "error": str(e),
                "error_type": "unexpected_error"
            }
    
    def _get_severity_breakdown(self, document: StandardDocument) -> Dict[str, int]:
        """获取严重程度分布"""
        breakdown = {
            "critical": 0,
            "high": 0,
            "medium": 0,
            "low": 0
        }
        
        for rule in document.get_all_rules():
            breakdown[rule.severity.value] += 1
        
        return breakdown
    
    async def _get_storage_stats(self) -> Dict[str, Any]:
        """获取存储统计"""
        try:
            # 计算各目录的大小
            standards_size = await file_manager.get_directory_size(config.standards_dir)
            cache_size = await file_manager.get_directory_size(config.cache_dir)
            index_size = await file_manager.get_file_size(config.get_index_file_path())
            
            return {
                "standards_directory_size": standards_size,
                "cache_directory_size": cache_size,
                "index_file_size": index_size,
                "total_size": standards_size + cache_size + index_size,
                "standards_path": str(config.standards_dir),
                "cache_path": str(config.cache_dir),
                "index_path": str(config.get_index_file_path())
            }
            
        except Exception as e:
            logger.error(f"Error getting storage stats: {e}")
            return {"error": str(e)}
    
    async def _check_system_health(self) -> Dict[str, Any]:
        """检查系统健康状态"""
        health_checks = {
            "directories_exist": True,
            "index_accessible": True,
            "cache_accessible": True,
            "permissions_ok": True
        }
        
        issues = []
        
        try:
            # 检查目录是否存在
            if not config.data_dir.exists():
                health_checks["directories_exist"] = False
                issues.append("Data directory does not exist")
            
            # 检查索引是否可访问
            try:
                await index_manager.get_index_stats()
            except Exception as e:
                health_checks["index_accessible"] = False
                issues.append(f"Index not accessible: {e}")
            
            # 检查缓存是否可访问
            try:
                await cache_manager.get_cache_stats()
            except Exception as e:
                health_checks["cache_accessible"] = False
                issues.append(f"Cache not accessible: {e}")
            
            # 检查权限
            try:
                test_file = config.data_dir / "test_permissions.tmp"
                await file_manager.write_text(test_file, "test")
                await file_manager.remove_file(test_file)
            except Exception as e:
                health_checks["permissions_ok"] = False
                issues.append(f"Permission issue: {e}")
            
        except Exception as e:
            issues.append(f"Health check error: {e}")
        
        overall_health = all(health_checks.values())
        
        return {
            "overall_healthy": overall_health,
            "checks": health_checks,
            "issues": issues,
            "status": "healthy" if overall_health else "unhealthy"
        }


# 全局规范管理器实例
standards_manager = StandardsManager()
