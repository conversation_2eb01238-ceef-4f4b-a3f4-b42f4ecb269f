"""
编码规范数据模型

定义规范文档、规则、章节等数据结构
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum


class SeverityLevel(str, Enum):
    """规则严重程度枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RuleExample(BaseModel):
    """规则示例模型"""
    title: str = Field(..., description="示例标题")
    good_code: Optional[str] = Field(None, description="正确代码示例")
    bad_code: Optional[str] = Field(None, description="错误代码示例")
    explanation: Optional[str] = Field(None, description="示例说明")


class StandardRule(BaseModel):
    """编码规范规则模型"""
    id: str = Field(..., description="规则唯一标识符")
    title: str = Field(..., description="规则标题")
    description: str = Field(..., description="规则描述")
    severity: SeverityLevel = Field(..., description="严重程度")
    rationale: Optional[str] = Field(None, description="规则理由说明")
    examples: List[RuleExample] = Field(default_factory=list, description="示例代码")
    tags: List[str] = Field(default_factory=list, description="规则标签")
    references: List[str] = Field(default_factory=list, description="参考链接")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self.model_dump()


class StandardSection(BaseModel):
    """规范章节模型"""
    id: str = Field(..., description="章节唯一标识符")
    title: str = Field(..., description="章节标题")
    description: Optional[str] = Field(None, description="章节描述")
    rules: List[StandardRule] = Field(default_factory=list, description="章节规则列表")
    subsections: List['StandardSection'] = Field(default_factory=list, description="子章节")
    
    def get_all_rules(self) -> List[StandardRule]:
        """获取章节及其子章节的所有规则"""
        all_rules = self.rules.copy()
        for subsection in self.subsections:
            all_rules.extend(subsection.get_all_rules())
        return all_rules
    
    def find_rule_by_id(self, rule_id: str) -> Optional[StandardRule]:
        """根据ID查找规则"""
        for rule in self.rules:
            if rule.id == rule_id:
                return rule
        
        for subsection in self.subsections:
            found_rule = subsection.find_rule_by_id(rule_id)
            if found_rule:
                return found_rule
        
        return None


class StandardReference(BaseModel):
    """外部参考资料模型"""
    title: str = Field(..., description="参考标题")
    url: str = Field(..., description="参考URL")
    description: Optional[str] = Field(None, description="参考描述")
    type: str = Field(default="external", description="参考类型")


class StandardMetadata(BaseModel):
    """规范元数据模型"""
    language: str = Field(..., description="编程语言")
    framework: Optional[str] = Field(None, description="框架名称")
    version: str = Field(..., description="规范版本")
    last_updated: datetime = Field(..., description="最后更新时间")
    source: str = Field(..., description="来源URL")
    description: str = Field(..., description="规范描述")
    author: Optional[str] = Field(None, description="规范作者")
    license: Optional[str] = Field(None, description="许可证")


class StandardDocument(BaseModel):
    """完整的编码规范文档模型"""
    metadata: StandardMetadata = Field(..., description="规范元数据")
    sections: List[StandardSection] = Field(default_factory=list, description="规范章节")
    references: List[StandardReference] = Field(default_factory=list, description="外部参考资料")
    
    def get_all_rules(self) -> List[StandardRule]:
        """获取文档中的所有规则"""
        all_rules = []
        for section in self.sections:
            all_rules.extend(section.get_all_rules())
        return all_rules
    
    def find_rule_by_id(self, rule_id: str) -> Optional[StandardRule]:
        """根据ID查找规则"""
        for section in self.sections:
            found_rule = section.find_rule_by_id(rule_id)
            if found_rule:
                return found_rule
        return None
    
    def search_rules(self, query: str, case_sensitive: bool = False) -> List[StandardRule]:
        """搜索包含指定关键词的规则"""
        if not case_sensitive:
            query = query.lower()
        
        matching_rules = []
        for rule in self.get_all_rules():
            # 搜索标题、描述和标签
            search_text = f"{rule.title} {rule.description} {' '.join(rule.tags)}"
            if not case_sensitive:
                search_text = search_text.lower()
            
            if query in search_text:
                matching_rules.append(rule)
        
        return matching_rules
    
    def get_rules_by_severity(self, severity: SeverityLevel) -> List[StandardRule]:
        """根据严重程度获取规则"""
        return [rule for rule in self.get_all_rules() if rule.severity == severity]
    
    def get_rules_by_tag(self, tag: str) -> List[StandardRule]:
        """根据标签获取规则"""
        return [rule for rule in self.get_all_rules() if tag in rule.tags]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self.model_dump()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StandardDocument':
        """从字典创建实例"""
        return cls.model_validate(data)


# 更新前向引用
StandardSection.model_rebuild()


class StandardSummary(BaseModel):
    """规范摘要信息（用于列表显示）"""
    language: str = Field(..., description="编程语言")
    framework: Optional[str] = Field(None, description="框架名称")
    version: str = Field(..., description="规范版本")
    description: str = Field(..., description="规范描述")
    last_updated: datetime = Field(..., description="最后更新时间")
    rule_count: int = Field(..., description="规则总数")
    source: str = Field(..., description="来源URL")
    
    @classmethod
    def from_document(cls, document: StandardDocument) -> 'StandardSummary':
        """从完整文档创建摘要"""
        return cls(
            language=document.metadata.language,
            framework=document.metadata.framework,
            version=document.metadata.version,
            description=document.metadata.description,
            last_updated=document.metadata.last_updated,
            rule_count=len(document.get_all_rules()),
            source=document.metadata.source
        )
