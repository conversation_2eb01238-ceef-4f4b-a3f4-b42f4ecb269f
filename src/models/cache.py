"""
缓存管理数据模型

定义缓存条目、注册表等数据结构
"""

from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel, Field
from pathlib import Path


class CacheEntry(BaseModel):
    """缓存条目模型"""
    key: str = Field(..., description="缓存键")
    file_path: str = Field(..., description="缓存文件路径")
    created_at: datetime = Field(..., description="创建时间")
    last_accessed: datetime = Field(..., description="最后访问时间")
    access_count: int = Field(default=0, description="访问次数")
    size_bytes: int = Field(default=0, description="文件大小（字节）")
    content_type: str = Field(default="standard", description="内容类型")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="额外元数据")
    
    def is_valid(self, max_age_hours: int = 24) -> bool:
        """检查缓存是否有效"""
        max_age = timedelta(hours=max_age_hours)
        return datetime.now() - self.created_at < max_age
    
    def update_access(self):
        """更新访问信息"""
        self.last_accessed = datetime.now()
        self.access_count += 1
    
    def get_age_hours(self) -> float:
        """获取缓存年龄（小时）"""
        return (datetime.now() - self.created_at).total_seconds() / 3600
    
    def get_unused_hours(self) -> float:
        """获取未使用时间（小时）"""
        return (datetime.now() - self.last_accessed).total_seconds() / 3600


class CacheStats(BaseModel):
    """缓存统计信息模型"""
    total_size_bytes: int = Field(default=0, description="总大小（字节）")
    entry_count: int = Field(default=0, description="条目数量")
    hit_count: int = Field(default=0, description="命中次数")
    miss_count: int = Field(default=0, description="未命中次数")
    last_cleanup: Optional[datetime] = Field(None, description="最后清理时间")
    
    @property
    def hit_rate(self) -> float:
        """命中率"""
        total_requests = self.hit_count + self.miss_count
        if total_requests == 0:
            return 0.0
        return self.hit_count / total_requests
    
    @property
    def total_size_mb(self) -> float:
        """总大小（MB）"""
        return self.total_size_bytes / (1024 * 1024)
    
    def record_hit(self):
        """记录缓存命中"""
        self.hit_count += 1
    
    def record_miss(self):
        """记录缓存未命中"""
        self.miss_count += 1


class CacheRegistry(BaseModel):
    """缓存注册表模型"""
    entries: Dict[str, CacheEntry] = Field(default_factory=dict, description="缓存条目字典")
    stats: CacheStats = Field(default_factory=CacheStats, description="缓存统计")
    
    def add_entry(self, entry: CacheEntry):
        """添加缓存条目"""
        self.entries[entry.key] = entry
        self.stats.entry_count = len(self.entries)
        self._update_total_size()
    
    def get_entry(self, key: str) -> Optional[CacheEntry]:
        """获取缓存条目"""
        entry = self.entries.get(key)
        if entry:
            entry.update_access()
            self.stats.record_hit()
            return entry
        else:
            self.stats.record_miss()
            return None
    
    def remove_entry(self, key: str) -> bool:
        """移除缓存条目"""
        if key in self.entries:
            del self.entries[key]
            self.stats.entry_count = len(self.entries)
            self._update_total_size()
            return True
        return False
    
    def get_expired_entries(self, max_age_hours: int = 24) -> list[str]:
        """获取过期的缓存条目键列表"""
        expired_keys = []
        for key, entry in self.entries.items():
            if not entry.is_valid(max_age_hours):
                expired_keys.append(key)
        return expired_keys
    
    def get_unused_entries(self, unused_hours: int = 72) -> list[str]:
        """获取长时间未使用的缓存条目键列表"""
        unused_keys = []
        for key, entry in self.entries.items():
            if entry.get_unused_hours() > unused_hours:
                unused_keys.append(key)
        return unused_keys
    
    def get_large_entries(self, size_threshold_mb: float = 10.0) -> list[str]:
        """获取大文件缓存条目键列表"""
        threshold_bytes = size_threshold_mb * 1024 * 1024
        large_keys = []
        for key, entry in self.entries.items():
            if entry.size_bytes > threshold_bytes:
                large_keys.append(key)
        return large_keys
    
    def cleanup_expired(self, max_age_hours: int = 24) -> int:
        """清理过期缓存条目"""
        expired_keys = self.get_expired_entries(max_age_hours)
        for key in expired_keys:
            self.remove_entry(key)
        
        if expired_keys:
            self.stats.last_cleanup = datetime.now()
        
        return len(expired_keys)
    
    def cleanup_unused(self, unused_hours: int = 72) -> int:
        """清理长时间未使用的缓存条目"""
        unused_keys = self.get_unused_entries(unused_hours)
        for key in unused_keys:
            self.remove_entry(key)
        
        if unused_keys:
            self.stats.last_cleanup = datetime.now()
        
        return len(unused_keys)
    
    def cleanup_by_size(self, max_total_size_mb: float = 100.0) -> int:
        """按大小清理缓存（移除最旧的条目）"""
        max_size_bytes = max_total_size_mb * 1024 * 1024
        
        if self.stats.total_size_bytes <= max_size_bytes:
            return 0
        
        # 按创建时间排序，最旧的在前
        sorted_entries = sorted(
            self.entries.items(),
            key=lambda x: x[1].created_at
        )
        
        removed_count = 0
        for key, entry in sorted_entries:
            if self.stats.total_size_bytes <= max_size_bytes:
                break
            
            self.remove_entry(key)
            removed_count += 1
        
        if removed_count > 0:
            self.stats.last_cleanup = datetime.now()
        
        return removed_count
    
    def _update_total_size(self):
        """更新总大小统计"""
        self.stats.total_size_bytes = sum(
            entry.size_bytes for entry in self.entries.values()
        )
    
    def get_summary(self) -> Dict[str, Any]:
        """获取缓存摘要信息"""
        return {
            "entry_count": self.stats.entry_count,
            "total_size_mb": round(self.stats.total_size_mb, 2),
            "hit_rate": round(self.stats.hit_rate * 100, 2),
            "hit_count": self.stats.hit_count,
            "miss_count": self.stats.miss_count,
            "last_cleanup": self.stats.last_cleanup.isoformat() if self.stats.last_cleanup else None,
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self.model_dump()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CacheRegistry':
        """从字典创建实例"""
        return cls.model_validate(data)
