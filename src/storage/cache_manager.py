"""
缓存管理模块

提供缓存生命周期管理、清理策略等功能
"""

import hashlib
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from ..config.settings import config
from ..models.cache import CacheEntry, CacheRegistry
from .file_manager import file_manager

logger = logging.getLogger("CodeStandardMCP.CacheManager")


class CacheManager:
    """缓存管理器类"""

    def __init__(self):
        self.registry_path = config.get_cache_registry_path()
        self._registry: Optional[CacheRegistry] = None

    async def _load_registry(self) -> CacheRegistry:
        """加载缓存注册表"""
        if self._registry is None:
            registry_data = await file_manager.read_json(self.registry_path)
            if registry_data:
                try:
                    self._registry = CacheRegistry.from_dict(registry_data)
                    logger.debug("Cache registry loaded successfully")
                except Exception as e:
                    logger.error(f"Error loading cache registry: {e}")
                    self._registry = CacheRegistry()
            else:
                self._registry = CacheRegistry()
                logger.debug("Created new cache registry")

        return self._registry

    async def _save_registry(self) -> bool:
        """保存缓存注册表"""
        if self._registry is None:
            return False

        try:
            registry_data = self._registry.to_dict()
            success = await file_manager.write_json(self.registry_path, registry_data)
            if success:
                logger.debug("Cache registry saved successfully")
            return success
        except Exception as e:
            logger.error(f"Error saving cache registry: {e}")
            return False

    def _generate_cache_key(
        self, url: str, domain: str = "", additional_params: Dict[str, Any] = None
    ) -> str:
        """生成缓存键"""
        # 组合所有参数
        key_parts = [url, domain]
        if additional_params:
            # 按键排序确保一致性
            sorted_params = sorted(additional_params.items())
            key_parts.extend([f"{k}:{v}" for k, v in sorted_params])

        # 生成哈希
        key_string = "|".join(key_parts)
        return hashlib.md5(key_string.encode("utf-8")).hexdigest()

    async def get_cached_content(
        self, url: str, domain: str = "", additional_params: Dict[str, Any] = None
    ) -> Optional[Any]:
        """获取缓存内容"""
        try:
            cache_key = self._generate_cache_key(url, domain, additional_params)
            registry = await self._load_registry()

            entry = registry.get_entry(cache_key)
            if entry is None:
                logger.debug(f"Cache miss for key: {cache_key}")
                return None

            # 检查缓存是否有效
            if not entry.is_valid(config.cache_duration_hours):
                logger.debug(f"Cache expired for key: {cache_key}")
                await self.remove_cached_content(cache_key)
                return None

            # 读取缓存文件
            cache_file_path = Path(entry.file_path)
            if entry.content_type == "json":
                content = await file_manager.read_json(cache_file_path)
            else:
                content = await file_manager.read_text(cache_file_path)

            if content is not None:
                logger.debug(f"Cache hit for key: {cache_key}")
                await self._save_registry()  # 保存更新的访问统计
                return content
            else:
                # 文件不存在或读取失败，清理注册表
                await self.remove_cached_content(cache_key)
                return None

        except Exception as e:
            logger.error(f"Error getting cached content: {e}")
            return None

    async def store_cached_content(
        self,
        url: str,
        content: Any,
        domain: str = "",
        content_type: str = "json",
        additional_params: Dict[str, Any] = None,
    ) -> bool:
        """存储内容到缓存"""
        try:
            cache_key = self._generate_cache_key(url, domain, additional_params)
            cache_file_path = config.get_cache_file_path(cache_key)

            # 写入缓存文件
            if content_type == "json":
                success = await file_manager.write_json(cache_file_path, content)
            else:
                success = await file_manager.write_text(cache_file_path, str(content))

            if not success:
                return False

            # 创建缓存条目
            file_size = file_manager.get_file_size(cache_file_path)
            now = datetime.now()

            entry = CacheEntry(
                key=cache_key,
                file_path=str(cache_file_path),
                created_at=now,
                last_accessed=now,
                access_count=1,
                size_bytes=file_size,
                content_type=content_type,
                metadata={
                    "url": url,
                    "domain": domain,
                    "additional_params": additional_params or {},
                },
            )

            # 更新注册表
            registry = await self._load_registry()
            registry.add_entry(entry)

            # 保存注册表
            await self._save_registry()

            logger.debug(f"Content cached successfully with key: {cache_key}")
            return True

        except Exception as e:
            logger.error(f"Error storing cached content: {e}")
            return False

    async def remove_cached_content(self, cache_key: str) -> bool:
        """移除缓存内容"""
        try:
            registry = await self._load_registry()
            entry = registry.entries.get(cache_key)

            if entry:
                # 删除缓存文件
                cache_file_path = Path(entry.file_path)
                file_manager.delete_file(cache_file_path)

                # 从注册表移除
                registry.remove_entry(cache_key)
                await self._save_registry()

                logger.debug(f"Cache entry removed: {cache_key}")
                return True

            return False

        except Exception as e:
            logger.error(f"Error removing cached content: {e}")
            return False

    async def cleanup_expired_cache(self, max_age_hours: Optional[int] = None) -> int:
        """清理过期缓存"""
        try:
            if max_age_hours is None:
                max_age_hours = config.cache_duration_hours

            registry = await self._load_registry()
            expired_keys = registry.get_expired_entries(max_age_hours)

            removed_count = 0
            for cache_key in expired_keys:
                if await self.remove_cached_content(cache_key):
                    removed_count += 1

            if removed_count > 0:
                logger.info(f"Cleaned up {removed_count} expired cache entries")

            return removed_count

        except Exception as e:
            logger.error(f"Error cleaning up expired cache: {e}")
            return 0

    async def cleanup_unused_cache(self, unused_hours: int = 72) -> int:
        """清理长时间未使用的缓存"""
        try:
            registry = await self._load_registry()
            unused_keys = registry.get_unused_entries(unused_hours)

            removed_count = 0
            for cache_key in unused_keys:
                if await self.remove_cached_content(cache_key):
                    removed_count += 1

            if removed_count > 0:
                logger.info(f"Cleaned up {removed_count} unused cache entries")

            return removed_count

        except Exception as e:
            logger.error(f"Error cleaning up unused cache: {e}")
            return 0

    async def cleanup_by_size(self, max_size_mb: Optional[float] = None) -> int:
        """按大小清理缓存"""
        try:
            if max_size_mb is None:
                max_size_mb = config.max_cache_size_mb

            registry = await self._load_registry()
            removed_count = registry.cleanup_by_size(max_size_mb)

            # 删除被移除的缓存文件
            for cache_key in list(registry.entries.keys()):
                entry = registry.entries.get(cache_key)
                if entry:
                    cache_file_path = Path(entry.file_path)
                    if not cache_file_path.exists():
                        registry.remove_entry(cache_key)

            await self._save_registry()

            if removed_count > 0:
                logger.info(f"Cleaned up {removed_count} cache entries by size")

            return removed_count

        except Exception as e:
            logger.error(f"Error cleaning up cache by size: {e}")
            return 0

    async def clear_all_cache(self) -> int:
        """清空所有缓存"""
        try:
            registry = await self._load_registry()
            cache_keys = list(registry.entries.keys())

            removed_count = 0
            for cache_key in cache_keys:
                if await self.remove_cached_content(cache_key):
                    removed_count += 1

            # 清理空目录
            file_manager.cleanup_empty_directories(config.cache_dir)

            logger.info(f"Cleared all cache: {removed_count} entries removed")
            return removed_count

        except Exception as e:
            logger.error(f"Error clearing all cache: {e}")
            return 0

    async def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        try:
            registry = await self._load_registry()
            summary = registry.get_summary()

            # 添加额外信息
            summary.update(
                {
                    "cache_duration_hours": config.cache_duration_hours,
                    "max_cache_size_mb": config.max_cache_size_mb,
                    "cache_directory": str(config.cache_dir),
                    "registry_file": str(self.registry_path),
                }
            )

            return summary

        except Exception as e:
            logger.error(f"Error getting cache info: {e}")
            return {"error": str(e)}


# 全局缓存管理器实例
cache_manager = CacheManager()
