"""
索引管理模块

提供规范索引的创建、更新、查询功能
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path
import logging

from ..models.standard import StandardDocument, StandardSummary
from ..config.settings import config
from .file_manager import file_manager

logger = logging.getLogger('CodeStandardMCP.IndexManager')


class StandardsIndex:
    """规范索引数据结构"""
    
    def __init__(self):
        self.languages: Dict[str, Dict[str, Any]] = {}
        self.metadata = {
            "last_updated": datetime.now().isoformat(),
            "standard_count": 0,
            "version": "1.0"
        }
    
    def add_standard(self, summary: StandardSummary, file_path: str):
        """添加规范到索引"""
        language = summary.language.lower()
        framework = summary.framework.lower() if summary.framework else "general"
        
        if language not in self.languages:
            self.languages[language] = {"frameworks": {}, "general": None}
        
        standard_info = {
            "path": file_path,
            "version": summary.version,
            "last_updated": summary.last_updated.isoformat(),
            "rule_count": summary.rule_count,
            "description": summary.description,
            "source": summary.source,
        }
        
        if framework == "general":
            self.languages[language]["general"] = standard_info
        else:
            self.languages[language]["frameworks"][framework] = standard_info
        
        self._update_metadata()
    
    def remove_standard(self, language: str, framework: Optional[str] = None):
        """从索引中移除规范"""
        language = language.lower()
        framework = framework.lower() if framework else "general"
        
        if language in self.languages:
            if framework == "general":
                self.languages[language]["general"] = None
            elif framework in self.languages[language]["frameworks"]:
                del self.languages[language]["frameworks"][framework]
            
            # 如果语言下没有任何规范，移除整个语言条目
            if (not self.languages[language]["general"] and 
                not self.languages[language]["frameworks"]):
                del self.languages[language]
        
        self._update_metadata()
    
    def get_standard_info(self, language: str, framework: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """获取规范信息"""
        language = language.lower()
        framework = framework.lower() if framework else "general"
        
        if language not in self.languages:
            return None
        
        if framework == "general":
            return self.languages[language]["general"]
        else:
            return self.languages[language]["frameworks"].get(framework)
    
    def list_languages(self) -> List[str]:
        """列出所有支持的语言"""
        return list(self.languages.keys())
    
    def list_frameworks(self, language: str) -> List[str]:
        """列出指定语言的所有框架"""
        language = language.lower()
        if language not in self.languages:
            return []
        
        frameworks = list(self.languages[language]["frameworks"].keys())
        if self.languages[language]["general"]:
            frameworks.append("general")
        
        return frameworks
    
    def search_standards(self, query: str) -> List[Dict[str, Any]]:
        """搜索规范"""
        query = query.lower()
        results = []
        
        for language, lang_data in self.languages.items():
            # 搜索通用规范
            if lang_data["general"]:
                standard = lang_data["general"]
                if (query in language or 
                    query in standard["description"].lower() or
                    query in standard.get("source", "").lower()):
                    results.append({
                        "language": language,
                        "framework": None,
                        **standard
                    })
            
            # 搜索框架规范
            for framework, standard in lang_data["frameworks"].items():
                if (query in language or 
                    query in framework or
                    query in standard["description"].lower() or
                    query in standard.get("source", "").lower()):
                    results.append({
                        "language": language,
                        "framework": framework,
                        **standard
                    })
        
        return results
    
    def _update_metadata(self):
        """更新元数据"""
        self.metadata["last_updated"] = datetime.now().isoformat()
        
        # 计算规范总数
        count = 0
        for lang_data in self.languages.values():
            if lang_data["general"]:
                count += 1
            count += len(lang_data["frameworks"])
        
        self.metadata["standard_count"] = count
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "languages": self.languages,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StandardsIndex':
        """从字典创建实例"""
        index = cls()
        index.languages = data.get("languages", {})
        index.metadata = data.get("metadata", index.metadata)
        return index


class IndexManager:
    """索引管理器类"""
    
    def __init__(self):
        self.index_path = config.get_index_file_path()
        self._index: Optional[StandardsIndex] = None
    
    async def _load_index(self) -> StandardsIndex:
        """加载索引"""
        if self._index is None:
            index_data = await file_manager.read_json(self.index_path)
            if index_data:
                try:
                    self._index = StandardsIndex.from_dict(index_data)
                    logger.debug("Standards index loaded successfully")
                except Exception as e:
                    logger.error(f"Error loading standards index: {e}")
                    self._index = StandardsIndex()
            else:
                self._index = StandardsIndex()
                logger.debug("Created new standards index")
        
        return self._index
    
    async def _save_index(self) -> bool:
        """保存索引"""
        if self._index is None:
            return False
        
        try:
            index_data = self._index.to_dict()
            success = await file_manager.write_json(self.index_path, index_data)
            if success:
                logger.debug("Standards index saved successfully")
            return success
        except Exception as e:
            logger.error(f"Error saving standards index: {e}")
            return False
    
    async def add_standard(self, document: StandardDocument, file_path: str) -> bool:
        """添加规范到索引"""
        try:
            summary = StandardSummary.from_document(document)
            index = await self._load_index()
            index.add_standard(summary, file_path)
            return await self._save_index()
        except Exception as e:
            logger.error(f"Error adding standard to index: {e}")
            return False
    
    async def remove_standard(self, language: str, framework: Optional[str] = None) -> bool:
        """从索引中移除规范"""
        try:
            index = await self._load_index()
            index.remove_standard(language, framework)
            return await self._save_index()
        except Exception as e:
            logger.error(f"Error removing standard from index: {e}")
            return False
    
    async def get_standard_info(self, language: str, framework: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """获取规范信息"""
        try:
            index = await self._load_index()
            return index.get_standard_info(language, framework)
        except Exception as e:
            logger.error(f"Error getting standard info: {e}")
            return None
    
    async def list_languages(self) -> List[str]:
        """列出所有支持的语言"""
        try:
            index = await self._load_index()
            return index.list_languages()
        except Exception as e:
            logger.error(f"Error listing languages: {e}")
            return []
    
    async def list_frameworks(self, language: str) -> List[str]:
        """列出指定语言的所有框架"""
        try:
            index = await self._load_index()
            return index.list_frameworks(language)
        except Exception as e:
            logger.error(f"Error listing frameworks: {e}")
            return []
    
    async def list_all_standards(self, language: Optional[str] = None, 
                               framework: Optional[str] = None) -> List[Dict[str, Any]]:
        """列出所有规范"""
        try:
            index = await self._load_index()
            results = []
            
            for lang, lang_data in index.languages.items():
                # 如果指定了语言，只处理该语言
                if language and lang != language.lower():
                    continue
                
                # 处理通用规范
                if lang_data["general"] and (not framework or framework.lower() == "general"):
                    results.append({
                        "language": lang,
                        "framework": None,
                        **lang_data["general"]
                    })
                
                # 处理框架规范
                for fw, standard in lang_data["frameworks"].items():
                    if not framework or fw == framework.lower():
                        results.append({
                            "language": lang,
                            "framework": fw,
                            **standard
                        })
            
            return results
        except Exception as e:
            logger.error(f"Error listing all standards: {e}")
            return []
    
    async def search_standards(self, query: str) -> List[Dict[str, Any]]:
        """搜索规范"""
        try:
            index = await self._load_index()
            return index.search_standards(query)
        except Exception as e:
            logger.error(f"Error searching standards: {e}")
            return []
    
    async def get_index_stats(self) -> Dict[str, Any]:
        """获取索引统计信息"""
        try:
            index = await self._load_index()
            return {
                "total_languages": len(index.languages),
                "total_standards": index.metadata["standard_count"],
                "last_updated": index.metadata["last_updated"],
                "version": index.metadata["version"],
                "index_file": str(self.index_path),
                "languages": index.list_languages(),
            }
        except Exception as e:
            logger.error(f"Error getting index stats: {e}")
            return {"error": str(e)}
    
    async def rebuild_index(self) -> bool:
        """重建索引"""
        try:
            logger.info("Rebuilding standards index...")
            
            # 创建新索引
            new_index = StandardsIndex()
            
            # 扫描所有规范文件
            for language_dir in config.standards_dir.iterdir():
                if not language_dir.is_dir():
                    continue
                
                language = language_dir.name
                
                for standard_file in language_dir.glob("*.json"):
                    framework = standard_file.stem if standard_file.stem != "general" else None
                    
                    # 读取规范文档
                    document_data = await file_manager.read_json(standard_file)
                    if document_data:
                        try:
                            document = StandardDocument.from_dict(document_data)
                            summary = StandardSummary.from_document(document)
                            new_index.add_standard(summary, str(standard_file))
                        except Exception as e:
                            logger.error(f"Error processing standard file {standard_file}: {e}")
            
            # 保存新索引
            self._index = new_index
            success = await self._save_index()
            
            if success:
                logger.info("Standards index rebuilt successfully")
            
            return success
            
        except Exception as e:
            logger.error(f"Error rebuilding index: {e}")
            return False


# 全局索引管理器实例
index_manager = IndexManager()
