[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "code-standard-mcp"
version = "1.0.0"
description = "CodeStandardMCP - 编码规范服务工具，提供规范查询、合规检查和管理功能的MCP服务器"
readme = "README.md"
requires-python = ">=3.10"
license = {text = "MIT"}
authors = [
    {name = "CodeStandardMCP", email = "<EMAIL>"},
]
keywords = ["mcp", "coding-standards", "compliance", "code-quality", "fastmcp", "async", "standards-as-service"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Software Development :: Code Generators",
    "Framework :: AsyncIO",
]
dependencies = [
    "httpx>=0.28.1",
    "fastmcp>=2.0.0",
    "pydantic>=2.0.0",
    "aiofiles>=23.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-httpx>=0.21.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "isort>=5.12.0",
]

[project.urls]
Homepage = "https://github.com/example/code-standard-mcp"
Repository = "https://github.com/example/code-standard-mcp"
Issues = "https://github.com/example/code-standard-mcp/issues"
Documentation = "https://github.com/example/code-standard-mcp/blob/main/README.md"

[project.scripts]
code-standard-mcp = "main:main"

[tool.hatch.build.targets.wheel]
include = [
    "main.py",
    "src/",
    "data/config.example.json",
    "README.md",
]

[tool.hatch.build.targets.sdist]
include = [
    "main.py",
    "src/",
    "data/config.example.json",
    "README.md",
    "pyproject.toml",
]

# 代码质量工具配置
[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
]
markers = [
    "slow: marks tests as slow",
    "integration: marks tests as integration tests",
]

# UV 包管理器配置 - 使用中国镜像源
[tool.uv]
index-url = "https://pypi.tuna.tsinghua.edu.cn/simple"
extra-index-url = [
    "https://mirrors.aliyun.com/pypi/simple/",
    "https://pypi.douban.com/simple/"
]
