"""
CodeStandardMCP - 编码规范即服务 MCP 服务器

基于 FastMCP 2.0 框架实现的编码规范管理服务器
提供规范获取、搜索、合规性检查和管理功能
"""

import asyncio
import json
import logging
import sys
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastmcp import FastMCP

# 导入核心模块
from src.config.settings import config
from src.services.compliance_checker import compliance_checker
from src.services.standard_explorer import standard_explorer
from src.services.standard_fetcher import standard_fetcher
from src.services.standards_manager import standards_manager


# 设置日志系统
def setup_logging():
    """设置日志系统"""
    log_level = getattr(logging, config.log_level.upper(), logging.INFO)
    logging.basicConfig(
        level=log_level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(sys.stderr)],
    )
    return logging.getLogger("CodeStandardMCP")


logger = setup_logging()


# 自定义序列化器
def custom_serializer(data):
    """自定义数据序列化器，支持更好的JSON格式化"""
    if isinstance(data, dict):
        return json.dumps(data, ensure_ascii=False, indent=2, sort_keys=True)
    elif isinstance(data, (list, tuple)):
        return json.dumps(data, ensure_ascii=False, indent=2)
    else:
        return json.dumps(data, ensure_ascii=False, indent=2)


# 错误处理工具函数
def create_error_response(
    error_type: str, error_message: str, **extra_data
) -> Dict[str, Any]:
    """创建标准化的错误响应"""
    response = {
        "success": False,
        "error_type": error_type,
        "error": error_message,
        "timestamp": datetime.now().isoformat(),
        **extra_data,
    }

    logger.error(f"Error [{error_type}]: {error_message}")
    return response


def create_success_response(data: Any, **extra_data) -> Dict[str, Any]:
    """创建标准化的成功响应"""
    response = {"success": True, "timestamp": datetime.now().isoformat(), **extra_data}

    if isinstance(data, dict):
        response.update(data)
    else:
        response["data"] = data

    return response


# 创建 FastMCP 服务器实例
mcp = FastMCP(
    name="code_standard_mcp",
    instructions="""
    CodeStandardMCP - 编码规范即服务

    这是一个专业的编码规范管理MCP服务器，提供以下核心功能：

    🔍 规范获取 (StandardFetcher)：
    - fetch_standard: 从外部API获取编码规范并缓存到本地

    📚 规范浏览 (StandardExplorer)：
    - list_standards: 列出所有可用的编码规范
    - search_standard: 在规范中搜索特定内容
    - get_rule_details: 获取特定规则的详细信息

    ✅ 合规性检查 (ComplianceChecker)：
    - check_compliance: 检查代码是否符合编码规范
    - explain_violation: 解释违规规则和修复建议

    🛠️ 规范管理 (StandardsManager)：
    - update_standards: 更新本地规范数据
    - validate_standard: 验证规范文档的完整性
    - clean_cache: 清理缓存和临时文件

    支持的编程语言：Python, JavaScript, Java, C#, Go, Rust, PHP, Ruby 等
    支持的框架：Django, React, Spring, .NET Core, Express 等

    环境变量配置：
    - CSMCP_DATA_DIR: 数据存储目录
    - CSMCP_CACHE_TTL: 缓存生存时间
    - CSMCP_LOG_LEVEL: 日志级别
    - CSMCP_HTTP_TIMEOUT: HTTP请求超时时间
    """,
    dependencies=[
        "fastmcp>=2.0.0",
        "pydantic>=2.0.0",
        "aiofiles>=23.0.0",
        "httpx>=0.28.1",
    ],
    include_tags={"public", "standards", "compliance", "management"},
    exclude_tags={"internal", "deprecated"},
    tool_serializer=custom_serializer,
)

# ==================== MCP 工具实现 ====================


@mcp.tool(tags={"public", "standards", "fetcher"})
async def fetch_standard(
    domain: str,
    language: str,
    framework: Optional[str] = None,
    force_refresh: bool = False,
) -> str:
    """
    从外部API获取编码规范并缓存到本地

    这个工具从指定的外部API端点获取编码规范文档，支持多种编程语言和框架。
    获取的规范会被解析、验证并存储到本地文件系统中，同时建立索引以便快速搜索。

    Args:
        domain: 规范领域（如 "coding_standards", "api_guidelines" 等）
        language: 编程语言（如 "python", "javascript", "java" 等）
        framework: 可选的框架名称（如 "django", "react", "spring" 等）
        force_refresh: 是否强制刷新，忽略现有缓存（默认：false）

    Returns:
        JSON格式的字符串，包含获取结果、规范信息和统计数据

    Examples:
        fetch_standard("coding_standards", "python", "django")
        fetch_standard("api_guidelines", "javascript", "react", force_refresh=true)
    """
    try:
        logger.info(f"Fetching standard: {domain}/{language}/{framework}")

        result = await standard_fetcher.fetch_standard(
            domain=domain,
            language=language,
            framework=framework,
            force_refresh=force_refresh,
        )

        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        error_response = create_error_response(
            "tool_execution",
            f"Failed to fetch standard: {str(e)}",
            domain=domain,
            language=language,
            framework=framework,
        )
        return json.dumps(error_response, ensure_ascii=False, indent=2)


@mcp.tool(tags={"public", "standards", "explorer"})
async def list_standards(
    language: Optional[str] = None, framework: Optional[str] = None
) -> str:
    """
    列出所有可用的编码规范

    这个工具提供本地存储的所有编码规范的概览，可以按编程语言和框架进行过滤。
    返回的信息包括规范的基本元数据、版本信息和最后更新时间。

    Args:
        language: 可选的编程语言过滤器
        framework: 可选的框架过滤器

    Returns:
        JSON格式的字符串，包含规范列表和统计信息

    Examples:
        list_standards()
        list_standards("python")
        list_standards("javascript", "react")
    """
    try:
        logger.info(f"Listing standards: language={language}, framework={framework}")

        result = await standard_explorer.list_standards(
            language=language, framework=framework
        )

        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        error_response = create_error_response(
            "tool_execution",
            f"Failed to list standards: {str(e)}",
            language=language,
            framework=framework,
        )
        return json.dumps(error_response, ensure_ascii=False, indent=2)


@mcp.tool(tags={"public", "standards", "search"})
async def search_standard(
    query: str,
    language: Optional[str] = None,
    framework: Optional[str] = None,
    max_results: int = 10,
) -> str:
    """
    在编码规范中搜索特定内容

    这个工具在本地存储的编码规范中执行全文搜索，支持关键词匹配、模糊搜索和上下文提取。
    搜索结果按相关性排序，并提供匹配的上下文信息。

    Args:
        query: 搜索查询字符串
        language: 可选的编程语言过滤器
        framework: 可选的框架过滤器
        max_results: 最大返回结果数量（默认：10）

    Returns:
        JSON格式的字符串，包含搜索结果和匹配的上下文

    Examples:
        search_standard("variable naming")
        search_standard("error handling", "python")
        search_standard("async await", "javascript", "react", max_results=5)
    """
    try:
        logger.info(
            f"Searching standards: query='{query}', "
            f"language={language}, framework={framework}"
        )

        result = await standard_explorer.search_standard(
            query=query, language=language, framework=framework, max_results=max_results
        )

        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        error_response = create_error_response(
            "tool_execution",
            f"Failed to search standards: {str(e)}",
            query=query,
            language=language,
            framework=framework,
        )
        return json.dumps(error_response, ensure_ascii=False, indent=2)


@mcp.tool(tags={"public", "standards", "details"})
async def get_rule_details(
    rule_id: str, language: str, framework: Optional[str] = None
) -> str:
    """
    获取特定规则的详细信息

    这个工具提供编码规范中特定规则的完整详细信息，包括规则描述、示例代码、
    最佳实践建议和相关规则链接。

    Args:
        rule_id: 规则的唯一标识符
        language: 编程语言
        framework: 可选的框架名称

    Returns:
        JSON格式的字符串，包含规则的详细信息

    Examples:
        get_rule_details("PEP8-E501", "python")
        get_rule_details("ESLint-no-unused-vars", "javascript", "react")
    """
    try:
        logger.info(f"Getting rule details: {rule_id} for {language}/{framework}")

        result = await standard_explorer.get_rule_details(
            rule_id=rule_id, language=language, framework=framework
        )

        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        error_response = create_error_response(
            "tool_execution",
            f"Failed to get rule details: {str(e)}",
            rule_id=rule_id,
            language=language,
            framework=framework,
        )
        return json.dumps(error_response, ensure_ascii=False, indent=2)


@mcp.tool(tags={"public", "compliance", "checker"})
async def check_compliance(
    code: str,
    language: str,
    framework: Optional[str] = None,
    rule_ids: Optional[List[str]] = None,
) -> str:
    """
    检查代码是否符合编码规范

    这个工具分析提供的代码片段，检查其是否符合指定语言和框架的编码规范。
    返回详细的违规报告，包括违规位置、严重程度和修复建议。

    Args:
        code: 要检查的代码片段
        language: 编程语言
        framework: 可选的框架名称
        rule_ids: 可选的特定规则ID列表，如果提供则只检查这些规则

    Returns:
        JSON格式的字符串，包含合规性检查结果和违规详情

    Examples:
        check_compliance("def my_function():\\n    pass", "python")
        check_compliance("const x = 1;", "javascript", "react")
        check_compliance("public class Test {}", "java", rule_ids=["naming-convention"])
    """
    try:
        logger.info(f"Checking compliance for {language}/{framework} code")

        result = await compliance_checker.check_compliance(
            code=code, language=language, framework=framework, rule_ids=rule_ids
        )

        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        error_response = create_error_response(
            "tool_execution",
            f"Failed to check compliance: {str(e)}",
            language=language,
            framework=framework,
        )
        return json.dumps(error_response, ensure_ascii=False, indent=2)


@mcp.tool(tags={"public", "compliance", "explanation"})
async def explain_violation(
    rule_id: str, language: str, framework: Optional[str] = None
) -> str:
    """
    解释违规规则和提供修复建议

    这个工具提供特定违规规则的详细解释，包括为什么这个规则很重要、
    常见的违规情况、如何修复违规以及相关的最佳实践。

    Args:
        rule_id: 违规规则的标识符
        language: 编程语言
        framework: 可选的框架名称

    Returns:
        JSON格式的字符串，包含规则解释和修复建议

    Examples:
        explain_violation("PEP8-E501", "python")
        explain_violation("ESLint-no-unused-vars", "javascript", "react")
    """
    try:
        logger.info(f"Explaining violation: {rule_id} for {language}/{framework}")

        result = await compliance_checker.explain_violation(
            rule_id=rule_id, language=language, framework=framework
        )

        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        error_response = create_error_response(
            "tool_execution",
            f"Failed to explain violation: {str(e)}",
            rule_id=rule_id,
            language=language,
            framework=framework,
        )
        return json.dumps(error_response, ensure_ascii=False, indent=2)


@mcp.tool(tags={"public", "management", "update"})
async def update_standards(
    language: Optional[str] = None,
    framework: Optional[str] = None,
    force_update: bool = False,
) -> str:
    """
    更新本地编码规范数据

    这个工具从外部源更新本地存储的编码规范，可以更新所有规范或特定语言/框架的规范。
    支持增量更新和强制完全更新。

    Args:
        language: 可选的特定编程语言
        framework: 可选的特定框架
        force_update: 是否强制更新，忽略缓存和版本检查

    Returns:
        JSON格式的字符串，包含更新结果和统计信息

    Examples:
        update_standards()
        update_standards("python")
        update_standards("javascript", "react", force_update=true)
    """
    try:
        logger.info(
            f"Updating standards: language={language}, "
            f"framework={framework}, force={force_update}"
        )

        result = await standards_manager.update_standards(
            language=language, framework=framework, force_update=force_update
        )

        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        error_response = create_error_response(
            "tool_execution",
            f"Failed to update standards: {str(e)}",
            language=language,
            framework=framework,
        )
        return json.dumps(error_response, ensure_ascii=False, indent=2)


@mcp.tool(tags={"public", "management", "validation"})
async def validate_standard(language: str, framework: Optional[str] = None) -> str:
    """
    验证编码规范文档的完整性

    这个工具检查本地存储的编码规范文档是否完整、格式正确且符合预期的结构。
    提供详细的验证报告和错误诊断。

    Args:
        language: 编程语言
        framework: 可选的框架名称

    Returns:
        JSON格式的字符串，包含验证结果和详细报告

    Examples:
        validate_standard("python")
        validate_standard("javascript", "react")
    """
    try:
        logger.info(f"Validating standard: {language}/{framework}")

        result = await standards_manager.validate_standard(
            language=language, framework=framework
        )

        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        error_response = create_error_response(
            "tool_execution",
            f"Failed to validate standard: {str(e)}",
            language=language,
            framework=framework,
        )
        return json.dumps(error_response, ensure_ascii=False, indent=2)


@mcp.tool(tags={"public", "management", "cache"})
async def clean_cache(
    max_age_days: Optional[int] = None, max_size_mb: Optional[int] = None
) -> str:
    """
    清理缓存和临时文件

    这个工具清理过期的缓存文件、临时数据和未使用的索引，释放存储空间并优化性能。
    可以按年龄和大小进行选择性清理。

    Args:
        max_age_days: 可选的最大缓存年龄（天），超过此年龄的缓存将被清理
        max_size_mb: 可选的最大缓存大小（MB），超过此大小时清理最旧的缓存

    Returns:
        JSON格式的字符串，包含清理结果和释放的空间统计

    Examples:
        clean_cache()
        clean_cache(max_age_days=7)
        clean_cache(max_age_days=30, max_size_mb=100)
    """
    try:
        logger.info(
            f"Cleaning cache: max_age={max_age_days} days, max_size={max_size_mb} MB"
        )

        result = await standards_manager.clean_cache(
            max_age_days=max_age_days, max_size_mb=max_size_mb
        )

        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        error_response = create_error_response(
            "tool_execution", f"Failed to clean cache: {str(e)}"
        )
        return json.dumps(error_response, ensure_ascii=False, indent=2)


# ==================== 服务器启动和管理 ====================


async def initialize_system():
    """初始化系统组件"""
    try:
        logger.info("Initializing CodeStandardMCP system...")

        # 确保数据目录存在
        config.data_dir.mkdir(parents=True, exist_ok=True)
        config.standards_dir.mkdir(parents=True, exist_ok=True)
        config.cache_dir.mkdir(parents=True, exist_ok=True)

        logger.info("System initialization completed")

    except Exception as e:
        logger.error(f"System initialization failed: {e}")
        raise


async def cleanup_system():
    """清理系统资源"""
    try:
        logger.info("Cleaning up system resources...")
        # 这里可以添加任何需要的清理逻辑
        logger.info("System cleanup completed")

    except Exception as e:
        logger.error(f"System cleanup failed: {e}")


async def async_main():
    """异步主函数"""
    logger.info("Starting CodeStandardMCP Server...")
    logger.info(
        f"Configuration: data_dir={config.data_dir}, log_level={config.log_level}"
    )
    logger.info("Server will run with STDIO transport")

    try:
        # 初始化系统
        await initialize_system()

        # 启动 MCP 服务器 (STDIO transport)
        logger.info("Starting STDIO MCP server")
        await mcp.run_async(transport="stdio")

    except KeyboardInterrupt:
        logger.info("Server interrupted by user")
    except Exception as e:
        logger.error(f"Server error: {str(e)}")
    finally:
        # 清理资源
        await cleanup_system()
        logger.info("Server shutdown complete")


def main():
    """主函数，启动 CodeStandardMCP 服务器"""
    try:
        asyncio.run(async_main())
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Application error: {str(e)}")


if __name__ == "__main__":
    main()
