"""
基础测试 - 确保项目基本结构正常
"""

import pytest
from pathlib import Path


def test_project_structure():
    """测试项目结构是否正确"""
    project_root = Path(__file__).parent.parent
    
    # 检查主要目录存在
    assert (project_root / "src").exists()
    assert (project_root / "main.py").exists()
    assert (project_root / "pyproject.toml").exists()
    
    # 检查源代码目录结构
    src_dir = project_root / "src"
    assert (src_dir / "config").exists()
    assert (src_dir / "models").exists()
    assert (src_dir / "services").exists()
    assert (src_dir / "storage").exists()
    assert (src_dir / "utils").exists()


def test_imports():
    """测试主要模块可以正常导入"""
    try:
        from src.config.settings import ServerConfig
        from src.models.standard import StandardDocument
        from src.services.standard_fetcher import StandardFetcher
        assert True
    except ImportError as e:
        pytest.fail(f"Import failed: {e}")


@pytest.mark.asyncio
async def test_basic_async():
    """测试异步功能基础"""
    import asyncio
    await asyncio.sleep(0.001)  # 基本异步测试
    assert True
